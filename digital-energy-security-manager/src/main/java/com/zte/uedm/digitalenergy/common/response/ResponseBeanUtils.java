package com.zte.uedm.digitalenergy.common.response;

import com.zte.uedm.basis.exception.ErrorCodeOptional;
import com.zte.uedm.basis.exception.UedmException;
import com.zte.uedm.basis.util.base.response.bean.ResponseBean;

/**
 * ResponseBeanUtils
 * API 接口返回组装工具类
 */
public class ResponseBeanUtils
{
    private ResponseBeanUtils()
    {

    }

    public static ResponseBean<String> getResponseBeanByInvalidParameter()
    {
        return ResponseBean.of(ErrorCodeOptional.INVALID_PARAMETER);
    }


    public static ResponseBean<String> getResponseBeanByUedmException(UedmException e)
    {
        return ResponseBean.of(e);
    }


    public static ResponseBean<String> getResponseBeanByException(Exception e)
    {
        return ResponseBean.failed();
    }

    public static <T> ResponseBean<T> getNormalResponseBean(Integer code, T data, Integer total)
    {
        return new ResponseBean<>(code, data, total);
    }

    public static <T> ResponseBean<T> getNormalResponseBean(T data, Integer total)
    {
        return ResponseBean.success(data, total);
    }
    public static <T> ResponseBean<T> getNormalResponseBean(String message, T data, Integer total)
    {
        return new ResponseBean<>(ErrorCodeOptional.SUCCESS.getCode(), message, data, total);
    }


    public static <T> ResponseBean<T> getNormalResponseBean(Integer code, String message, T data, Integer total)
    {
        return new ResponseBean<>(code, message, data, total);
    }
    public static <T> ResponseBean<T> getNormalResponseBean(Integer code, String error, String message, T data, Integer total)
    {
        return new ResponseBean<>(code, error, message, data, total);
    }

    public static ResponseBean<String> getFailedResponseBean()
    {
        return ResponseBean.of(ErrorCodeOptional.FAILED);
    }

}
