package com.zte.uedm.digitalenergy.common.response.bean;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.zte.uedm.basis.exception.ErrorCodeOptional;
import com.zte.uedm.basis.exception.UedmException;
import com.zte.uedm.basis.util.base.i18n.I18nUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@ToString
public class ResponseBean<T>
{
    private Integer code;
    private String error;
    private String message;
    private T data;
    private Integer total;

    public ResponseBean() {}

    public ResponseBean(Integer code, String message)
    {
        this(code, message, null);
    }
    public ResponseBean(Integer code, String message, T data)
    {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    public ResponseBean(Integer code, T data, Integer total)
    {
        this.code = code;
        this.data = data;
        this.total = total;
    }

    public ResponseBean(Integer code, String message, T data, Integer total)
    {
        this.code = code;
        this.message = message;
        this.data = data;
        this.total = total;
    }

    public ResponseBean(Integer code, String error, String message, T data, Integer total)
    {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    public ResponseBean(ErrorCodeOptional optional)
    {
        this(optional.getCode(), optional.getI18nDesc(), null);
    }

    public ResponseBean(ErrorCodeOptional optional, T data)
    {
        this(optional.getCode(), optional.getI18nDesc(), data);
    }

    public ResponseBean(UedmException e)
    {
        this(e.getErrorId(), e.getErrorDesc(), null);
    }


    public static <T> ResponseBean<T> of(Integer code, String message)
    {
        return new ResponseBean<>(code, message);
    }
    public static <T> ResponseBean<T> of(ErrorCodeOptional optional)
    {
        return new ResponseBean<>(optional);
    }
    public static <T> ResponseBean<T> of(UedmException e)
    {
        return new ResponseBean<>(e);
    }


    public static <T> ResponseBean<T> success()
    {
        return new ResponseBean<>(ErrorCodeOptional.SUCCESS);
    }
    public static <T> ResponseBean<T> success(T data)
    {
        return new ResponseBean<>(ErrorCodeOptional.SUCCESS, data);
    }
    public static <T> ResponseBean<T> success(T data, Integer total)
    {
        ResponseBean<T> res = new ResponseBean<>(ErrorCodeOptional.SUCCESS, data);
        res.setTotal(total);
        return res;
    }

    public static <T> ResponseBean<T> failed()
    {
        return new ResponseBean<>(ErrorCodeOptional.FAILED);
    }
    public static <T> ResponseBean<T> failed(String message)
    {
        return new ResponseBean<>(ErrorCodeOptional.FAILED.getCode(), message);
    }

//    public static <T> ResponseBean<T> error()
//    {
//        return new ResponseBean<>(StatusCode.SYS_ERROR);
//    }
//
//    public static <T> ResponseBean<T> error(String message)
//    {
//        return new ResponseBean<>(StatusCode.SYS_ERROR.getCode(), message);
//    }

    @JsonIgnore
    public boolean isSuccess()
    {
        return ErrorCodeOptional.SUCCESS.getCode().equals(this.code);
    }

    public boolean isI18nMessage()
    {
        return I18nUtils.isI18nJson(this.message);
    }

}
